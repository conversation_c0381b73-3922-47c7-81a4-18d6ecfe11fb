import { NextRequest, NextResponse } from 'next/server';
import jsPDF from 'jspdf';

export async function POST(request: NextRequest) {
  try {
    // Create a test PDF with embedded data similar to what the template system generates
    const testEmbeddedData = {
      templateId: "good-moral-certificate",
      templateName: "Good_moral_certificate",
      placeholders: [
        "[AGE]",
        "[BARANGAY]",
        "[CTC NUMBER]",
        "[DAY]",
        "[FIRST NAME]",
        "[LAST NAME]",
        "[MIDDLE INITIAL]",
        "[MONTH]",
        "[O.R. NUMBER]",
        "[SUFFIX]",
        "[TIN NUMBER]",
        "[YEAR]"
      ],
      userData: {
        "AGE": "21",
        "BARANGAY": "San Roque",
        "CTC NUMBER": "13444434",
        "DAY": "",
        "FIRST NAME": "Brent Lemmuel",
        "LAST NAME": "Ortega",
        "MIDDLE INITIAL": "L.",
        "MONTH": "",
        "O.R. NUMBER": "13444434",
        "SUFFIX": "Jr.",
        "TIN NUMBER": "4324353",
        "YEAR": ""
      },
      photoBase64: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAA...",
      generatedAt: new Date().toISOString(),
      layoutSize: "A4"
    };

    // Create a new PDF
    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Add some visible content
    pdf.setFontSize(16);
    pdf.text("Test PDF Document", 20, 30);
    pdf.setFontSize(12);
    pdf.text("This is a test PDF generated for scanner testing.", 20, 50);
    pdf.text("Template: Good Moral Certificate", 20, 70);
    pdf.text("Applicant: Brent Lemmuel L. Ortega Jr.", 20, 90);

    // Add the embedded data as PDF metadata
    pdf.setProperties({
      title: `${testEmbeddedData.templateName} - Brent Lemmuel L. Ortega Jr.`,
      subject: `Generated document with embedded data - Template ID: ${testEmbeddedData.templateId}`,
      creator: "LDIS Template System",
      keywords: `template,${testEmbeddedData.templateId},embedded-data`,
    });

    // Add embedded data as invisible text at the bottom of the PDF for scanning
    pdf.setFontSize(1);
    pdf.setTextColor(255, 255, 255); // White text (invisible on white background)
    const embeddedDataString = `EMBEDDED_DATA_START:${JSON.stringify(testEmbeddedData)}:EMBEDDED_DATA_END`;
    pdf.text(embeddedDataString, 10, pdf.internal.pageSize.getHeight() - 2);

    // Convert PDF to buffer
    const pdfBuffer = Buffer.from(pdf.output('arraybuffer'));

    // Return the PDF as a downloadable file
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="test-document.pdf"',
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Error creating test PDF:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create test PDF',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
