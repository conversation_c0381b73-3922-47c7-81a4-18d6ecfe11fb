"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FileText, Upload, Download, Scan } from "lucide-react";
import { toast } from "sonner";

export default function PDFScannerPage() {
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [scanResult, setScanResult] = useState<any>(null);
  const [scanError, setScanError] = useState<string | null>(null);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    setFile(selectedFile);
    setScanResult(null);
    setScanError(null);
  };

  // Handle scan button click
  const handleScan = async () => {
    if (!file) {
      toast.error("Please select a PDF file to scan");
      return;
    }

    setIsLoading(true);
    setScanResult(null);
    setScanError(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      // Try the main scanner first
      const response = await fetch("/api/pdf-scanner/scan", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        // If main scanner fails, try the alternative scanner
        const altResponse = await fetch("/api/pdf-scanner/scan-alt", {
          method: "POST",
          body: formData,
        });

        if (!altResponse.ok) {
          const errorData = await altResponse.json();
          throw new Error(errorData.message || "Failed to scan PDF");
        }

        const altResult = await altResponse.json();
        setScanResult(altResult);
        toast.success("PDF scanned successfully (using alternative method)");
      } else {
        const result = await response.json();
        setScanResult(result);
        toast.success("PDF scanned successfully");
      }
    } catch (error) {
      console.error("Error scanning PDF:", error);
      setScanError(
        error instanceof Error
          ? error.message
          : "An unknown error occurred while scanning the PDF"
      );
      toast.error("Failed to scan PDF");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle create test PDF button click
  const handleCreateTestPDF = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/pdf-scanner/create-test-pdf", {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Failed to create test PDF");
      }

      // Get the PDF blob
      const blob = await response.blob();
      
      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "test-document.pdf";
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success("Test PDF created and downloaded");
    } catch (error) {
      console.error("Error creating test PDF:", error);
      toast.error("Failed to create test PDF");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">PDF Scanner</h1>
      <p className="mb-6 text-gray-600">
        Upload a PDF document generated by the LDIS Template System to extract its embedded data.
      </p>

      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <div className="mb-6">
          <Label htmlFor="pdf-file" className="block mb-2">
            Select PDF File
          </Label>
          <div className="flex gap-4">
            <Input
              id="pdf-file"
              type="file"
              accept=".pdf"
              onChange={handleFileChange}
              className="flex-1"
            />
            <Button
              onClick={handleScan}
              disabled={!file || isLoading}
              className="flex items-center gap-2"
            >
              <Scan className="w-4 h-4" />
              {isLoading ? "Scanning..." : "Scan PDF"}
            </Button>
          </div>
          {file && (
            <p className="mt-2 text-sm text-gray-500">
              Selected file: {file.name} ({(file.size / 1024).toFixed(2)} KB)
            </p>
          )}
        </div>

        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleCreateTestPDF}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Create Test PDF
          </Button>
        </div>
      </div>

      {scanError && (
        <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-8">
          <h3 className="text-red-800 font-medium mb-2">Error Scanning PDF</h3>
          <p className="text-red-700">{scanError}</p>
        </div>
      )}

      {scanResult && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Scan Results
          </h2>
          
          {scanResult.data && (
            <>
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Template Information</h3>
                <div className="grid grid-cols-2 gap-4 bg-gray-50 p-4 rounded-md">
                  <div>
                    <p className="text-sm text-gray-500">Template ID</p>
                    <p className="font-medium">{scanResult.data.templateId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Template Name</p>
                    <p className="font-medium">{scanResult.data.templateName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Layout Size</p>
                    <p className="font-medium">{scanResult.data.layoutSize || "Unknown"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Generated At</p>
                    <p className="font-medium">
                      {scanResult.data.generatedAt
                        ? new Date(scanResult.data.generatedAt).toLocaleString()
                        : "Unknown"}
                    </p>
                  </div>
                </div>
              </div>

              {scanResult.data.userData && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2">User Data</h3>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(scanResult.data.userData).map(([key, value]) => (
                        <div key={key}>
                          <p className="text-sm text-gray-500">{key}</p>
                          <p className="font-medium">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {scanResult.data.photoBase64 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2">Applicant Photo</h3>
                  <div className="bg-gray-50 p-4 rounded-md flex justify-center">
                    <img
                      src={scanResult.data.photoBase64}
                      alt="Applicant Photo"
                      className="max-h-48 border border-gray-300"
                    />
                  </div>
                </div>
              )}

              {scanResult.data.placeholders && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Template Placeholders</h3>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <div className="flex flex-wrap gap-2">
                      {scanResult.data.placeholders.map((placeholder: string) => (
                        <span
                          key={placeholder}
                          className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                        >
                          {placeholder}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          <div className="mt-6 pt-4 border-t border-gray-200">
            <details>
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                View Raw JSON Data
              </summary>
              <pre className="mt-2 p-4 bg-gray-50 rounded-md overflow-auto text-xs">
                {JSON.stringify(scanResult, null, 2)}
              </pre>
            </details>
          </div>
        </div>
      )}
    </div>
  );
}
