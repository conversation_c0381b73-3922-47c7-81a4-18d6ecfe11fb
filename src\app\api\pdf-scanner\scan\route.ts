import { NextRequest, NextResponse } from 'next/server';
import pdf from 'pdf-parse';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No PDF file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      return NextResponse.json(
        { error: 'Only PDF files are supported' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Parse PDF content
    const pdfData = await pdf(buffer);
    
    // Extract embedded data from the PDF text
    const embeddedData = extractEmbeddedData(pdfData.text);
    
    if (!embeddedData) {
      return NextResponse.json(
        { 
          error: 'No embedded template data found in PDF',
          message: 'This PDF was not generated by the LDIS Template System or does not contain embedded data'
        },
        { status: 404 }
      );
    }

    // Return the extracted data
    return NextResponse.json({
      success: true,
      data: embeddedData,
      originalFilename: file.name,
      extractedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error scanning PDF:', error);
    return NextResponse.json(
      { 
        error: 'Failed to scan PDF',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// Helper function to extract embedded data from PDF text
function extractEmbeddedData(text: string): any | null {
  try {
    // Look for the embedded data pattern
    const embeddedDataRegex = /EMBEDDED_DATA_START:(.*?):EMBEDDED_DATA_END/s;
    const match = text.match(embeddedDataRegex);
    
    if (!match || !match[1]) {
      return null;
    }
    
    // Parse the JSON data
    const jsonData = JSON.parse(match[1]);
    
    // Validate that it has the expected structure
    if (!jsonData.templateId || !jsonData.templateName || !jsonData.placeholders || !jsonData.userData) {
      throw new Error('Invalid embedded data structure');
    }
    
    return jsonData;
  } catch (error) {
    console.error('Error extracting embedded data:', error);
    return null;
  }
}
