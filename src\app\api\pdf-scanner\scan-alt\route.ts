import { NextRequest, NextResponse } from 'next/server';
import pdf from 'pdf-parse';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No PDF file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      return NextResponse.json(
        { error: 'Only PDF files are supported' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Parse PDF content with metadata
    const pdfData = await pdf(buffer, {
      // Enable metadata extraction
      max: 0, // Extract all pages
    });
    
    // Try multiple extraction methods
    let embeddedData = null;
    let extractionMethod = '';
    
    // Method 1: Extract from text content
    embeddedData = extractEmbeddedDataFromText(pdfData.text);
    if (embeddedData) {
      extractionMethod = 'text_content';
    }
    
    // Method 2: Try to extract from PDF info/metadata if text method failed
    if (!embeddedData && pdfData.info) {
      embeddedData = extractEmbeddedDataFromMetadata(pdfData.info);
      if (embeddedData) {
        extractionMethod = 'metadata';
      }
    }
    
    // Method 3: Try to find JSON-like patterns in the raw text
    if (!embeddedData) {
      embeddedData = extractEmbeddedDataFromPatterns(pdfData.text);
      if (embeddedData) {
        extractionMethod = 'pattern_matching';
      }
    }
    
    if (!embeddedData) {
      return NextResponse.json(
        { 
          error: 'No embedded template data found in PDF',
          message: 'This PDF was not generated by the LDIS Template System or does not contain embedded data',
          debug: {
            textLength: pdfData.text.length,
            hasInfo: !!pdfData.info,
            infoKeys: pdfData.info ? Object.keys(pdfData.info) : [],
            textPreview: pdfData.text.substring(0, 500) + '...'
          }
        },
        { status: 404 }
      );
    }

    // Return the extracted data
    return NextResponse.json({
      success: true,
      data: embeddedData,
      extractionMethod,
      originalFilename: file.name,
      extractedAt: new Date().toISOString(),
      debug: {
        textLength: pdfData.text.length,
        hasInfo: !!pdfData.info,
        infoKeys: pdfData.info ? Object.keys(pdfData.info) : []
      }
    });

  } catch (error) {
    console.error('Error scanning PDF:', error);
    return NextResponse.json(
      { 
        error: 'Failed to scan PDF',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// Method 1: Extract from embedded text
function extractEmbeddedDataFromText(text: string): any | null {
  try {
    const embeddedDataRegex = /EMBEDDED_DATA_START:(.*?):EMBEDDED_DATA_END/s;
    const match = text.match(embeddedDataRegex);
    
    if (!match || !match[1]) {
      return null;
    }
    
    const jsonData = JSON.parse(match[1]);
    return validateEmbeddedData(jsonData) ? jsonData : null;
  } catch (error) {
    return null;
  }
}

// Method 2: Extract from PDF metadata
function extractEmbeddedDataFromMetadata(info: any): any | null {
  try {
    // Check if template ID is in keywords or subject
    if (info.Subject && info.Subject.includes('Template ID:')) {
      const templateIdMatch = info.Subject.match(/Template ID:\s*([^,\s]+)/);
      if (templateIdMatch) {
        // Try to reconstruct basic data from metadata
        return {
          templateId: templateIdMatch[1],
          templateName: info.Title ? info.Title.split(' - ')[0] : 'Unknown',
          extractedFromMetadata: true,
          metadata: info
        };
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}

// Method 3: Pattern matching for JSON-like structures
function extractEmbeddedDataFromPatterns(text: string): any | null {
  try {
    // Look for JSON patterns that might contain template data
    const jsonPatterns = [
      /\{[^{}]*"templateId"[^{}]*\}/g,
      /\{[^{}]*"templateName"[^{}]*\}/g,
      /\{[^{}]*"userData"[^{}]*\}/g
    ];
    
    for (const pattern of jsonPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        for (const match of matches) {
          try {
            const jsonData = JSON.parse(match);
            if (validateEmbeddedData(jsonData)) {
              return jsonData;
            }
          } catch (e) {
            continue;
          }
        }
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}

// Validate embedded data structure
function validateEmbeddedData(data: any): boolean {
  return data && 
         typeof data === 'object' && 
         (data.templateId || data.templateName || data.userData);
}
