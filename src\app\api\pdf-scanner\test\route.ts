import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      message: 'PDF Scanner API is working',
      endpoints: {
        scan: '/api/pdf-scanner/scan - POST with PDF file to extract embedded data',
        createTestPdf: '/api/pdf-scanner/create-test-pdf - POST to generate a test PDF with embedded data',
        test: '/api/pdf-scanner/test - GET to test if the API is working'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in test endpoint:', error);
    return NextResponse.json(
      { 
        error: 'Test endpoint failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
